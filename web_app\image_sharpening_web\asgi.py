"""
ASGI config for image_sharpening_web project.
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import sharpening_app.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'image_sharpening_web.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            sharpening_app.routing.websocket_urlpatterns
        )
    ),
})
