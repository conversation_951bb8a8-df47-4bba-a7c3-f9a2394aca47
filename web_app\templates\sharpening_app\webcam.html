{% extends 'sharpening_app/base.html' %}

{% block title %}Webcam Demo - Image Sharpening Web App{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold">Real-time Webcam Enhancement</h1>
                <p class="lead text-muted">See our AI model enhance your webcam feed in real-time</p>
            </div>

            <!-- Webcam Container -->
            <div class="webcam-container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Original Feed</h6>
                            </div>
                            <div class="card-body p-0">
                                <video id="webcam-video" autoplay muted class="w-100"></video>
                                <canvas id="webcam-canvas" style="display: none;"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Enhanced Feed</h6>
                            </div>
                            <div class="card-body p-0">
                                <img id="enhanced-feed" class="w-100" alt="Enhanced feed will appear here">
                                <div id="enhancement-placeholder" class="text-center p-5 text-muted">
                                    <i class="fas fa-magic" style="font-size: 3rem;"></i>
                                    <p class="mt-2">Enhanced feed will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controls -->
                <div class="webcam-controls text-center">
                    <div class="btn-group mb-3" role="group">
                        <button type="button" class="btn btn-success" id="start-webcam">
                            <i class="fas fa-play"></i> Start Webcam
                        </button>
                        <button type="button" class="btn btn-warning" id="toggle-enhancement" disabled>
                            <i class="fas fa-pause"></i> Pause Enhancement
                        </button>
                        <button type="button" class="btn btn-danger" id="stop-webcam" disabled>
                            <i class="fas fa-stop"></i> Stop Webcam
                        </button>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h6 class="text-primary mb-1" id="current-fps">0</h6>
                            <small class="text-muted">FPS</small>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-success mb-1" id="processing-time">0</h6>
                            <small class="text-muted">Processing (ms)</small>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-info mb-1" id="frames-processed">0</h6>
                            <small class="text-muted">Frames Processed</small>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-warning mb-1" id="enhancement-status">Stopped</h6>
                            <small class="text-muted">Status</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="alert alert-danger mt-3" id="webcam-error" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="webcam-error-text">An error occurred with the webcam.</span>
            </div>

            <!-- Instructions -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6>Instructions:</h6>
                    <ul class="mb-0">
                        <li>Click "Start Webcam" to begin capturing video from your camera</li>
                        <li>The enhanced feed will appear on the right side in real-time</li>
                        <li>Use "Pause Enhancement" to temporarily stop processing while keeping the webcam active</li>
                        <li>Monitor the performance statistics below the video feeds</li>
                        <li>Click "Stop Webcam" to end the session</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let webcamStream = null;
let enhancementActive = false;
let enhancementInterval = null;
let framesProcessed = 0;
let startTime = null;

$(document).ready(function() {
    $('#start-webcam').on('click', startWebcam);
    $('#toggle-enhancement').on('click', toggleEnhancement);
    $('#stop-webcam').on('click', stopWebcam);
});

async function startWebcam() {
    try {
        webcamStream = await navigator.mediaDevices.getUserMedia({ 
            video: { width: 640, height: 480 } 
        });
        
        const video = document.getElementById('webcam-video');
        video.srcObject = webcamStream;
        
        $('#start-webcam').prop('disabled', true);
        $('#toggle-enhancement').prop('disabled', false);
        $('#stop-webcam').prop('disabled', false);
        
        $('#enhancement-status').text('Ready');
        
        // Start enhancement automatically
        startEnhancement();
        
    } catch (error) {
        showWebcamError('Failed to access webcam: ' + error.message);
    }
}

function startEnhancement() {
    if (enhancementActive) return;
    
    enhancementActive = true;
    framesProcessed = 0;
    startTime = Date.now();
    
    $('#toggle-enhancement').html('<i class="fas fa-pause"></i> Pause Enhancement');
    $('#enhancement-status').text('Active');
    $('#enhancement-placeholder').hide();
    
    enhancementInterval = setInterval(processFrame, 100); // Process every 100ms
}

function stopEnhancement() {
    if (!enhancementActive) return;
    
    enhancementActive = false;
    
    if (enhancementInterval) {
        clearInterval(enhancementInterval);
        enhancementInterval = null;
    }
    
    $('#toggle-enhancement').html('<i class="fas fa-play"></i> Resume Enhancement');
    $('#enhancement-status').text('Paused');
}

function toggleEnhancement() {
    if (enhancementActive) {
        stopEnhancement();
    } else {
        startEnhancement();
    }
}

function stopWebcam() {
    if (webcamStream) {
        webcamStream.getTracks().forEach(track => track.stop());
        webcamStream = null;
    }
    
    stopEnhancement();
    
    $('#start-webcam').prop('disabled', false);
    $('#toggle-enhancement').prop('disabled', true);
    $('#stop-webcam').prop('disabled', true);
    
    $('#enhancement-status').text('Stopped');
    $('#enhancement-placeholder').show();
    $('#enhanced-feed').attr('src', '');
    
    // Reset stats
    $('#current-fps').text('0');
    $('#processing-time').text('0');
    $('#frames-processed').text('0');
}

function processFrame() {
    if (!enhancementActive || !webcamStream) return;
    
    const video = document.getElementById('webcam-video');
    const canvas = document.getElementById('webcam-canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // Draw current frame to canvas
    ctx.drawImage(video, 0, 0);
    
    // Convert to base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    
    // Send for enhancement
    const frameStartTime = Date.now();
    
    $.ajax({
        url: '/api/enhance-webcam-frame/',
        type: 'POST',
        data: JSON.stringify({ image: imageData }),
        contentType: 'application/json',
        success: function(response) {
            if (response.success) {
                $('#enhanced-feed').attr('src', response.enhanced_image);
                
                const processingTime = Date.now() - frameStartTime;
                framesProcessed++;
                
                // Update stats
                $('#processing-time').text(processingTime);
                $('#frames-processed').text(framesProcessed);
                
                if (startTime) {
                    const elapsed = (Date.now() - startTime) / 1000;
                    const fps = framesProcessed / elapsed;
                    $('#current-fps').text(fps.toFixed(1));
                }
            }
        },
        error: function(xhr) {
            console.error('Frame enhancement failed:', xhr.responseJSON);
        }
    });
}

function showWebcamError(message) {
    $('#webcam-error-text').text(message);
    $('#webcam-error').show();
}
</script>
{% endblock %}
