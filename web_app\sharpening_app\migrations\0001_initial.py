# Generated by Django 4.2.8 on 2025-06-28 18:36

from django.db import migrations, models
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ImageEnhancement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('original_image', models.ImageField(upload_to='original_images/')),
                ('enhanced_image', models.ImageField(blank=True, null=True, upload_to='enhanced_images/')),
                ('comparison_image', models.ImageField(blank=True, null=True, upload_to='comparison_images/')),
                ('upload_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('processing_time', models.FloatField(blank=True, null=True)),
                ('image_width', models.IntegerField(blank=True, null=True)),
                ('image_height', models.IntegerField(blank=True, null=True)),
                ('file_size', models.IntegerField(blank=True, null=True)),
                ('ssim_score', models.FloatField(blank=True, null=True)),
                ('psnr_score', models.FloatField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('error_message', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-upload_time'],
            },
        ),
        migrations.CreateModel(
            name='WebcamSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_start', models.DateTimeField(default=django.utils.timezone.now)),
                ('session_end', models.DateTimeField(blank=True, null=True)),
                ('frames_processed', models.IntegerField(default=0)),
                ('average_fps', models.FloatField(blank=True, null=True)),
                ('total_processing_time', models.FloatField(blank=True, null=True)),
                ('enhancement_enabled', models.BooleanField(default=True)),
                ('resolution_width', models.IntegerField(default=640)),
                ('resolution_height', models.IntegerField(default=480)),
            ],
            options={
                'ordering': ['-session_start'],
            },
        ),
    ]
