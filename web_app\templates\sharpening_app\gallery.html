{% extends 'sharpening_app/base.html' %}

{% block title %}Gallery - Image Sharpening Web App{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="text-center mb-5">
        <h1 class="display-5 fw-bold">Enhancement Gallery</h1>
        <p class="lead text-muted">Browse through recent image enhancements</p>
    </div>

    {% if enhancements %}
    <div class="row g-4">
        {% for enhancement in enhancements %}
        <div class="col-lg-4 col-md-6">
            <div class="card h-100">
                {% if enhancement.comparison_image %}
                <img src="{{ enhancement.comparison_image.url }}" class="card-img-top" alt="Enhancement comparison">
                {% endif %}
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">{{ enhancement.upload_time|date:"M d, Y H:i" }}</small>
                        <span class="badge bg-success">{{ enhancement.processing_time|floatformat:3 }}s</span>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">Resolution</small>
                            <p class="mb-0">{{ enhancement.image_width }}x{{ enhancement.image_height }}</p>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">File Size</small>
                            <p class="mb-0">{{ enhancement.file_size|filesizeformat }}</p>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <a href="/download/original/{{ enhancement.id }}/" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download"></i> Original
                        </a>
                        <a href="/download/enhanced/{{ enhancement.id }}/" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-download"></i> Enhanced
                        </a>
                        <a href="/download/comparison/{{ enhancement.id }}/" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-download"></i> Compare
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-images text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3">No Enhancements Yet</h4>
        <p class="text-muted">Upload your first image to see it appear in the gallery</p>
        <a href="{% url 'upload_image' %}" class="btn btn-primary">
            <i class="fas fa-upload"></i> Upload Image
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
