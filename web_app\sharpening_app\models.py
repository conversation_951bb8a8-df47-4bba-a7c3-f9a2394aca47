from django.db import models
from django.utils import timezone
import uuid


class ImageEnhancement(models.Model):
    """Model to store image enhancement records."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    original_image = models.ImageField(upload_to='original_images/')
    enhanced_image = models.ImageField(upload_to='enhanced_images/', null=True, blank=True)
    comparison_image = models.ImageField(upload_to='comparison_images/', null=True, blank=True)
    
    # Metadata
    upload_time = models.DateTimeField(default=timezone.now)
    processing_time = models.FloatField(null=True, blank=True)  # in seconds
    image_width = models.IntegerField(null=True, blank=True)
    image_height = models.IntegerField(null=True, blank=True)
    file_size = models.IntegerField(null=True, blank=True)  # in bytes
    
    # Quality metrics
    ssim_score = models.FloatField(null=True, blank=True)
    psnr_score = models.FloatField(null=True, blank=True)
    
    # Status
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    error_message = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-upload_time']
    
    def __str__(self):
        return f"Enhancement {self.id} - {self.status}"


class WebcamSession(models.Model):
    """Model to store webcam session information."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session_start = models.DateTimeField(default=timezone.now)
    session_end = models.DateTimeField(null=True, blank=True)
    frames_processed = models.IntegerField(default=0)
    average_fps = models.FloatField(null=True, blank=True)
    total_processing_time = models.FloatField(null=True, blank=True)
    
    # Session settings
    enhancement_enabled = models.BooleanField(default=True)
    resolution_width = models.IntegerField(default=640)
    resolution_height = models.IntegerField(default=480)
    
    class Meta:
        ordering = ['-session_start']
    
    def __str__(self):
        return f"Webcam Session {self.id}"
