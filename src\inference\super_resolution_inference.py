"""
Super-resolution inference engine for upscaling images to 1080p HD resolution.
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import cv2
from typing import Union, Tuple, Optional
import logging
from pathlib import Path

from ..models.student_models import LightweightSR

class SuperResolutionEngine:
    """Enhanced inference engine for super-resolution image enhancement."""
    
    def __init__(self, 
                 model_path: Optional[str] = None,
                 device: Optional[str] = None,
                 target_resolution: Tuple[int, int] = (1080, 1920)):
        """
        Initialize the super-resolution engine.
        
        Args:
            model_path: Path to the trained super-resolution model
            device: Device to run inference on ('cpu', 'cuda', or None for auto)
            target_resolution: Target output resolution (height, width)
        """
        self.target_resolution = target_resolution
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.model_loaded = False
        
        # Try to load the super-resolution model first, fallback to enhancement model
        if model_path:
            self.load_model(model_path)
        else:
            # Try to load the best available model
            self._load_best_available_model()
    
    def _load_best_available_model(self):
        """Load the best available model (super-resolution or enhancement)."""
        # Try super-resolution model first
        sr_model_path = "results/checkpoints/best_super_resolution_model.pth"
        if Path(sr_model_path).exists():
            print("Loading super-resolution model...")
            self.load_model(sr_model_path)
            return
        
        # Fallback to enhancement model
        enhancement_model_path = "results/checkpoints/best_student_model.pth"
        if Path(enhancement_model_path).exists():
            print("Super-resolution model not found, using enhancement model...")
            self.load_model(enhancement_model_path)
            return
        
        print("No trained models found. Creating default model...")
        self._create_default_model()
    
    def _create_default_model(self):
        """Create a default super-resolution model."""
        self.model = LightweightSR(
            in_channels=3,
            out_channels=3,
            channels=64,
            num_blocks=6,
            upscale_factor=2,
            use_attention=True,
            activation='relu'
        ).to(self.device)
        
        self.model.eval()
        self.model_loaded = True
        print("Created default super-resolution model")
    
    def load_model(self, model_path: str):
        """Load a trained model from checkpoint."""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Get model parameters
            if 'model_params' in checkpoint:
                params = checkpoint['model_params']
                self.model = LightweightSR(**params).to(self.device)
            else:
                # Fallback for older checkpoints
                self.model = LightweightSR(
                    in_channels=3,
                    out_channels=3,
                    channels=32,  # Default from enhancement model
                    num_blocks=4,
                    upscale_factor=1
                ).to(self.device)
            
            # Load state dict
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            
            self.model.eval()
            self.model_loaded = True
            
            # Check if this is a super-resolution model
            upscale_factor = getattr(self.model, 'upscale_factor', 1)
            if upscale_factor > 1:
                print(f"Loaded super-resolution model with {upscale_factor}x upscaling")
            else:
                print("Loaded enhancement model (no upscaling)")
                
        except Exception as e:
            print(f"Error loading model: {e}")
            self._create_default_model()
    
    def enhance_to_hd(self, 
                      image: Union[np.ndarray, Image.Image, str],
                      maintain_aspect_ratio: bool = True) -> np.ndarray:
        """
        Enhance image and upscale to HD resolution.
        
        Args:
            image: Input image (numpy array, PIL Image, or file path)
            maintain_aspect_ratio: Whether to maintain aspect ratio when scaling
            
        Returns:
            Enhanced HD image as numpy array (H, W, C)
        """
        if not self.model_loaded:
            raise RuntimeError("No model loaded")
        
        # Load and preprocess image
        if isinstance(image, str):
            image = Image.open(image).convert('RGB')
        elif isinstance(image, np.ndarray):
            image = Image.fromarray(image)
        
        original_size = image.size  # (width, height)
        
        # Step 1: AI Enhancement
        enhanced_image = self._ai_enhance(image)
        
        # Step 2: Intelligent upscaling to HD
        hd_image = self._upscale_to_hd(enhanced_image, maintain_aspect_ratio)
        
        return hd_image
    
    def _ai_enhance(self, image: Image.Image) -> np.ndarray:
        """Apply AI enhancement to the image."""
        # Convert to tensor
        transform = torch.nn.Sequential(
            torch.nn.functional.interpolate if hasattr(torch.nn.functional, 'interpolate') else lambda x: x
        )
        
        # Resize for model input if needed
        model_input_size = (512, 512)  # Reasonable size for processing
        image_resized = image.resize(model_input_size, Image.LANCZOS)
        
        # Convert to tensor
        image_tensor = torch.from_numpy(np.array(image_resized)).float() / 255.0
        image_tensor = image_tensor.permute(2, 0, 1).unsqueeze(0).to(self.device)
        
        # AI enhancement
        with torch.no_grad():
            enhanced_tensor = self.model(image_tensor)
            enhanced_tensor = torch.clamp(enhanced_tensor, 0, 1)
        
        # Convert back to numpy
        enhanced_np = enhanced_tensor.squeeze(0).permute(1, 2, 0).cpu().numpy()
        enhanced_np = (enhanced_np * 255).astype(np.uint8)
        
        return enhanced_np
    
    def _upscale_to_hd(self, 
                      image: np.ndarray, 
                      maintain_aspect_ratio: bool = True) -> np.ndarray:
        """
        Upscale image to HD resolution using advanced interpolation.
        
        Args:
            image: Input image as numpy array
            maintain_aspect_ratio: Whether to maintain aspect ratio
            
        Returns:
            HD resolution image
        """
        target_h, target_w = self.target_resolution
        current_h, current_w = image.shape[:2]
        
        if maintain_aspect_ratio:
            # Calculate scaling to fit within HD resolution
            scale_w = target_w / current_w
            scale_h = target_h / current_h
            scale = min(scale_w, scale_h)
            
            new_w = int(current_w * scale)
            new_h = int(current_h * scale)
            
            # Upscale using high-quality interpolation
            upscaled = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
            
            # Create HD canvas and center the image
            hd_image = np.zeros((target_h, target_w, 3), dtype=np.uint8)
            
            # Calculate centering offsets
            offset_x = (target_w - new_w) // 2
            offset_y = (target_h - new_h) // 2
            
            # Place upscaled image in center
            hd_image[offset_y:offset_y+new_h, offset_x:offset_x+new_w] = upscaled
            
        else:
            # Direct resize to HD resolution
            hd_image = cv2.resize(image, (target_w, target_h), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply additional sharpening for HD quality
        hd_image = self._apply_hd_sharpening(hd_image)
        
        return hd_image
    
    def _apply_hd_sharpening(self, image: np.ndarray) -> np.ndarray:
        """Apply additional sharpening optimized for HD resolution."""
        # Create sharpening kernel
        kernel = np.array([[-0.5, -1, -0.5],
                          [-1,   6, -1],
                          [-0.5, -1, -0.5]])
        
        # Apply sharpening
        sharpened = cv2.filter2D(image, -1, kernel)
        
        # Blend with original (subtle sharpening)
        alpha = 0.3
        result = cv2.addWeighted(image, 1 - alpha, sharpened, alpha, 0)
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        if not self.model_loaded:
            return {"loaded": False}
        
        total_params = sum(p.numel() for p in self.model.parameters())
        upscale_factor = getattr(self.model, 'upscale_factor', 1)
        
        return {
            "loaded": True,
            "parameters": total_params,
            "upscale_factor": upscale_factor,
            "target_resolution": self.target_resolution,
            "device": self.device,
            "model_type": "Super-Resolution" if upscale_factor > 1 else "Enhancement"
        }

# Global instance for web application
_global_sr_engine = None

def get_super_resolution_engine() -> SuperResolutionEngine:
    """Get the global super-resolution engine instance."""
    global _global_sr_engine
    if _global_sr_engine is None:
        _global_sr_engine = SuperResolutionEngine()
    return _global_sr_engine
