{% extends 'sharpening_app/base.html' %}

{% block title %}Home - Image Sharpening Web App{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">AI-Powered Image Sharpening</h1>
                <p class="lead mb-4">
                    Transform your blurry images into crystal-clear masterpieces using our state-of-the-art 
                    knowledge distillation model. Real-time processing with professional results.
                </p>
                <div class="d-flex gap-3">
                    <a href="{% url 'upload_image' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-upload"></i> Upload Image
                    </a>
                    <a href="{% url 'webcam_demo' %}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-video"></i> Try Webcam
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-magic" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold">Why Choose Our Image Sharpener?</h2>
                <p class="lead text-muted">Advanced AI technology meets user-friendly design</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card feature-card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-bolt text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">Lightning Fast</h5>
                        <p class="card-text">
                            Ultra-lightweight model processes images in milliseconds. 
                            Perfect for real-time applications and batch processing.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card feature-card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-brain text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">AI-Powered</h5>
                        <p class="card-text">
                            Built with knowledge distillation technology, combining the power 
                            of large models with the efficiency of lightweight networks.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card feature-card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-eye text-info" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="card-title">High Quality</h5>
                        <p class="card-text">
                            Maintains image quality while enhancing sharpness. 
                            Optimized for various image types and resolutions.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Performance Stats -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="text-center mb-4">Performance Statistics</h3>
                <div class="performance-stats">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h4 class="text-primary mb-1" id="total-enhancements">{{ total_enhancements }}</h4>
                            <p class="text-muted mb-0">Images Enhanced</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-success mb-1" id="avg-processing-time">-</h4>
                            <p class="text-muted mb-0">Avg Processing Time</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-info mb-1" id="avg-fps">-</h4>
                            <p class="text-muted mb-0">Average FPS</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning mb-1" id="model-status">
                                <i class="fas fa-check-circle"></i>
                            </h4>
                            <p class="text-muted mb-0">Model Status</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Enhancements -->
{% if recent_enhancements %}
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="text-center mb-4">Recent Enhancements</h3>
                <div class="row g-3">
                    {% for enhancement in recent_enhancements %}
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ enhancement.upload_time|date:"M d, Y H:i" }}</small>
                                    <span class="badge bg-success">{{ enhancement.processing_time|floatformat:3 }}s</span>
                                </div>
                                {% if enhancement.comparison_image %}
                                <img src="{{ enhancement.comparison_image.url }}" class="img-fluid mt-2" alt="Enhancement comparison">
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-4">
                    <a href="{% url 'gallery' %}" class="btn btn-outline-primary">
                        View All Enhancements <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load performance statistics
    $.get('/api/performance-stats/', function(data) {
        if (data.avg_processing_time) {
            $('#avg-processing-time').text((data.avg_processing_time * 1000).toFixed(1) + 'ms');
        }
        if (data.avg_fps) {
            $('#avg-fps').text(data.avg_fps.toFixed(1));
        }
        if (data.model_loaded) {
            $('#model-status').html('<i class="fas fa-check-circle text-success"></i>');
        } else {
            $('#model-status').html('<i class="fas fa-times-circle text-danger"></i>');
        }
    }).fail(function() {
        console.log('Failed to load performance stats');
    });
});
</script>
{% endblock %}
