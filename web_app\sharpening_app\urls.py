from django.urls import path
from . import views

# Removed app_name to simplify URL references

urlpatterns = [
    # Main pages
    path('', views.home, name='home'),
    path('upload/', views.upload_image, name='upload_image'),
    path('webcam/', views.webcam_demo, name='webcam_demo'),
    path('gallery/', views.gallery, name='gallery'),
    path('about/', views.about, name='about'),
    
    # API endpoints
    path('api/enhance-image/', views.enhance_image_api, name='enhance_image_api'),
    path('api/enhance-webcam-frame/', views.enhance_webcam_frame, name='enhance_webcam_frame'),
    path('api/get-enhancement/<uuid:enhancement_id>/', views.get_enhancement, name='get_enhancement'),
    path('api/delete-enhancement/<uuid:enhancement_id>/', views.delete_enhancement, name='delete_enhancement'),
    
    # Download endpoints
    path('download/original/<uuid:enhancement_id>/', views.download_original, name='download_original'),
    path('download/enhanced/<uuid:enhancement_id>/', views.download_enhanced, name='download_enhanced'),
    path('download/comparison/<uuid:enhancement_id>/', views.download_comparison, name='download_comparison'),
    
    # Performance monitoring
    path('api/performance-stats/', views.performance_stats, name='performance_stats'),
]
