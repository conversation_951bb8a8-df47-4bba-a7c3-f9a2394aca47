# Image Sharpening Knowledge Distillation Configuration

# Dataset Configuration
dataset:
  train_dir: "data/train"
  val_dir: "data/val"
  test_dir: "data/test"
  benchmark_dir: "data/benchmark"
  
  # Image preprocessing
  input_size: [256, 256]  # Training patch size for efficiency
  target_size: [1920, 1080]  # Target inference resolution
  
  # Data augmentation
  augmentation:
    horizontal_flip: true
    vertical_flip: true
    rotation: true
    color_jitter: true
  
  # Degradation simulation (for video conferencing conditions)
  degradation:
    downscale_methods: ["bicubic", "bilinear"]
    downscale_factors: [2, 3, 4]
    noise_levels: [0, 5, 10, 15]
    compression_quality: [70, 80, 90]

# Model Configuration
model:
  # Teacher model
  teacher:
    name: "ESRGAN"  # Options: ESRGAN, Real-ESRGAN, EDSR, SwinIR
    pretrained_path: "models/pretrained/esrgan_teacher.pth"
    freeze_weights: true
  
  # Student model
  student:
    name: "LightweightSR"
    channels: 64
    num_blocks: 4
    upscale_factor: 1
    activation: "relu"
    use_attention: false
    
# Training Configuration
training:
  batch_size: 8
  num_epochs: 5
  learning_rate: 1e-4
  weight_decay: 1e-4
  
  # Knowledge distillation
  distillation:
    temperature: 4.0
    alpha: 0.7  # Weight for distillation loss
    beta: 0.3   # Weight for task loss
  
  # Loss functions
  losses:
    pixel_loss: "l1"  # Options: l1, l2, huber
    perceptual_loss: true
    adversarial_loss: false
  
  # Optimization
  optimizer: "adam"
  scheduler: "cosine"
  warmup_epochs: 5
  
  # Checkpointing
  save_every: 10
  checkpoint_dir: "results/checkpoints"

# Evaluation Configuration
evaluation:
  metrics: ["ssim", "psnr", "lpips"]
  target_ssim: 0.90
  target_fps: 30
  
  # Benchmark dataset categories
  categories: ["text", "nature", "people", "animals", "games"]
  min_images_per_category: 20
  
  # Subjective evaluation
  subjective:
    num_evaluators: 10
    comparison_pairs: 50
    rating_scale: [1, 2, 3, 4, 5]  # 1=Poor, 5=Excellent

# Hardware Configuration
hardware:
  device: "cuda"  # Options: cuda, cpu
  mixed_precision: true
  num_workers: 4
  pin_memory: true

# Logging Configuration
logging:
  use_wandb: false
  project_name: "image-sharpening-kd"
  log_every: 100
  save_images: true
  num_sample_images: 8
