{% extends 'sharpening_app/base.html' %}

{% block title %}About - Image Sharpening Web App{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold">About Our Technology</h1>
                <p class="lead text-muted">Learn about the AI technology powering our image enhancement system</p>
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h3>Knowledge Distillation Approach</h3>
                    <p>
                        Our image sharpening system is built using advanced knowledge distillation techniques, 
                        where a lightweight student model learns from powerful teacher models to achieve 
                        high-quality results with minimal computational requirements.
                    </p>
                    
                    <h4>Key Features:</h4>
                    <ul>
                        <li><strong>Ultra-lightweight Model:</strong> Only 0.08 MB in size with 22,019 parameters</li>
                        <li><strong>Real-time Processing:</strong> Optimized for fast inference on various devices</li>
                        <li><strong>High Quality:</strong> Maintains image quality while enhancing sharpness</li>
                        <li><strong>Versatile:</strong> Works with various image types and resolutions</li>
                    </ul>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h3>Technical Specifications</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Model Architecture</h5>
                            <ul>
                                <li>LightweightSR CNN</li>
                                <li>Depthwise Separable Convolutions</li>
                                <li>Channel Attention Mechanisms</li>
                                <li>Residual Connections</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Performance Metrics</h5>
                            <ul>
                                <li>Model Size: < 0.1 MB</li>
                                <li>Parameters: ~22K</li>
                                <li>Inference Time: ~100ms (CPU)</li>
                                <li>Supported Formats: JPG, PNG, GIF</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-body">
                    <h3>How It Works</h3>
                    <div class="row">
                        <div class="col-md-4 text-center mb-3">
                            <i class="fas fa-upload text-primary" style="font-size: 3rem;"></i>
                            <h5 class="mt-2">1. Upload</h5>
                            <p>Upload your image through the web interface or use the webcam for real-time processing</p>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <i class="fas fa-brain text-success" style="font-size: 3rem;"></i>
                            <h5 class="mt-2">2. Process</h5>
                            <p>Our AI model analyzes and enhances the image using learned patterns from teacher models</p>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <i class="fas fa-download text-info" style="font-size: 3rem;"></i>
                            <h5 class="mt-2">3. Download</h5>
                            <p>Get your enhanced image with improved sharpness and clarity in seconds</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <h3>Project Information</h3>
                    <p>
                        This image sharpening web application is part of a comprehensive AI research project 
                        focused on knowledge distillation for computer vision tasks. The system demonstrates 
                        how complex deep learning models can be compressed into efficient, deployable solutions 
                        without significant loss in quality.
                    </p>
                    
                    <h4>Technologies Used:</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <ul>
                                <li>PyTorch (Deep Learning Framework)</li>
                                <li>Django (Web Framework)</li>
                                <li>OpenCV (Computer Vision)</li>
                                <li>NumPy (Numerical Computing)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul>
                                <li>Bootstrap (Frontend Framework)</li>
                                <li>JavaScript/jQuery (Interactive Features)</li>
                                <li>HTML5 Canvas (Image Processing)</li>
                                <li>WebRTC (Webcam Integration)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
