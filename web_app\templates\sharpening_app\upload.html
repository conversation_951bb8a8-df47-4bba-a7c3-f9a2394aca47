{% extends 'sharpening_app/base.html' %}

{% block title %}Upload Image - Image Sharpening Web App{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold">Upload Your Image</h1>
                <p class="lead text-muted">Select an image to enhance with our AI-powered sharpening technology</p>
            </div>

            <!-- Upload Area -->
            <div class="upload-area mb-4" id="upload-area">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt text-muted mb-3" style="font-size: 4rem;"></i>
                    <h4>Drag & Drop Your Image Here</h4>
                    <p class="text-muted mb-3">or click to browse files</p>
                    <input type="file" id="file-input" accept="image/*" style="display: none;">
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open"></i> Choose File
                    </button>
                    <div class="mt-3">
                        <small class="text-muted">Supported formats: JPG, PNG, GIF (Max size: 10MB)</small>
                    </div>
                </div>
            </div>

            <!-- Processing Status -->
            <div class="processing-spinner text-center" id="processing-status">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Processing...</span>
                </div>
                <p class="mt-2">Enhancing your image...</p>
            </div>

            <!-- Results -->
            <div id="results" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Enhancement Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Processing Time</h6>
                                <p id="processing-time" class="text-success fw-bold">-</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Enhancement ID</h6>
                                <p id="enhancement-id" class="text-muted">-</p>
                            </div>
                        </div>
                        
                        <!-- Image Comparison -->
                        <div class="image-comparison mt-3">
                            <img id="comparison-image" class="img-fluid rounded" alt="Before and After Comparison">
                        </div>

                        <!-- Download Buttons -->
                        <div class="text-center mt-4">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" id="download-original">
                                    <i class="fas fa-download"></i> Original
                                </button>
                                <button type="button" class="btn btn-outline-success" id="download-enhanced">
                                    <i class="fas fa-download"></i> Enhanced
                                </button>
                                <button type="button" class="btn btn-outline-info" id="download-comparison">
                                    <i class="fas fa-download"></i> Comparison
                                </button>
                            </div>
                        </div>

                        <!-- Upload Another -->
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-secondary" onclick="resetUpload()">
                                <i class="fas fa-plus"></i> Upload Another Image
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="alert alert-danger" id="error-message" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="error-text">An error occurred while processing your image.</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentEnhancementId = null;

$(document).ready(function() {
    const uploadArea = $('#upload-area');
    const fileInput = $('#file-input');

    // Drag and drop functionality
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        uploadArea.addClass('dragover');
    });

    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        uploadArea.removeClass('dragover');
    });

    uploadArea.on('drop', function(e) {
        e.preventDefault();
        uploadArea.removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });

    // File input change
    fileInput.on('change', function() {
        if (this.files.length > 0) {
            handleFileUpload(this.files[0]);
        }
    });

    // Click to upload
    uploadArea.on('click', function() {
        fileInput.click();
    });
});

function handleFileUpload(file) {
    // Validate file
    if (!file.type.startsWith('image/')) {
        showError('Please select a valid image file.');
        return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB
        showError('File size must be less than 10MB.');
        return;
    }

    // Hide previous results and errors
    $('#results').hide();
    $('#error-message').hide();
    
    // Show processing status
    $('#processing-status').show();

    // Create form data
    const formData = new FormData();
    formData.append('image', file);

    // Upload and enhance
    $.ajax({
        url: '/api/enhance-image/',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#processing-status').hide();
            
            if (response.success) {
                showResults(response);
            } else {
                showError(response.error || 'Enhancement failed');
            }
        },
        error: function(xhr) {
            $('#processing-status').hide();
            
            let errorMsg = 'Upload failed';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }
            showError(errorMsg);
        }
    });
}

function showResults(data) {
    currentEnhancementId = data.enhancement_id;
    
    $('#processing-time').text((data.processing_time * 1000).toFixed(1) + ' ms');
    $('#enhancement-id').text(data.enhancement_id);
    $('#comparison-image').attr('src', data.comparison_url);
    
    // Set up download buttons
    $('#download-original').off('click').on('click', function() {
        window.open(`/download/original/${currentEnhancementId}/`, '_blank');
    });
    
    $('#download-enhanced').off('click').on('click', function() {
        window.open(`/download/enhanced/${currentEnhancementId}/`, '_blank');
    });
    
    $('#download-comparison').off('click').on('click', function() {
        window.open(`/download/comparison/${currentEnhancementId}/`, '_blank');
    });
    
    $('#results').show();
}

function showError(message) {
    $('#error-text').text(message);
    $('#error-message').show();
}

function resetUpload() {
    $('#results').hide();
    $('#error-message').hide();
    $('#file-input').val('');
    currentEnhancementId = null;
}
</script>
{% endblock %}
