import os
import sys
import json
import time
import base64
import cv2
import numpy as np
import torch
from io import BytesIO
from PIL import Image
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.conf import settings
from django.utils import timezone
from django.db.models import Avg, Count
from .models import ImageEnhancement, WebcamSession

# Add the parent directory to sys.path to import the model
sys.path.append(os.path.join(settings.BASE_DIR.parent, 'src'))

try:
    from models.student_models import LightweightSR
except ImportError:
    # Fallback import path
    sys.path.append(os.path.join(settings.BASE_DIR.parent))
    from src.models.student_models import LightweightSR


class ImageSharpener:
    """Singleton class for the image sharpening model."""
    
    _instance = None
    _model = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._load_model()
        return cls._instance
    
    def _load_model(self):
        """Load the trained model."""
        try:
            if not os.path.exists(settings.MODEL_PATH):
                raise FileNotFoundError(f"Model not found: {settings.MODEL_PATH}")
            
            # Load checkpoint
            checkpoint = torch.load(settings.MODEL_PATH, map_location=settings.DEVICE)
            
            # Create model
            self._model = LightweightSR(
                in_channels=3,
                out_channels=3,
                channels=32,
                num_blocks=4,
                upscale_factor=1
            )
            
            # Load state dict
            self._model.load_state_dict(checkpoint['model_state_dict'])
            self._model.eval()
            self._model.to(settings.DEVICE)
            
            print(f"✓ Model loaded successfully")
            
        except Exception as e:
            print(f"✗ Error loading model: {e}")
            self._model = None
    
    def enhance_image(self, image_array):
        """Enhance an image array."""
        if self._model is None:
            raise RuntimeError("Model not loaded")
        
        try:
            # Preprocess
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                # Convert BGR to RGB
                image_rgb = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image_array
            
            # Normalize to [0, 1]
            image_tensor = torch.from_numpy(image_rgb).float() / 255.0
            
            # Add batch dimension and change to CHW format
            image_tensor = image_tensor.permute(2, 0, 1).unsqueeze(0).to(settings.DEVICE)
            
            # Inference
            start_time = time.time()
            with torch.no_grad():
                output = self._model(image_tensor)
            inference_time = time.time() - start_time
            
            # Postprocess
            output = output.squeeze(0).permute(1, 2, 0)
            output = torch.clamp(output, 0, 1)
            output_np = (output.cpu().numpy() * 255).astype(np.uint8)
            
            # Convert RGB to BGR for OpenCV
            output_bgr = cv2.cvtColor(output_np, cv2.COLOR_RGB2BGR)
            
            return output_bgr, inference_time
            
        except Exception as e:
            raise RuntimeError(f"Enhancement failed: {e}")


# Initialize the sharpener
sharpener = ImageSharpener()


def home(request):
    """Home page view."""
    recent_enhancements = ImageEnhancement.objects.filter(status='completed')[:6]
    
    context = {
        'recent_enhancements': recent_enhancements,
        'total_enhancements': ImageEnhancement.objects.filter(status='completed').count(),
    }
    return render(request, 'sharpening_app/home.html', context)


def upload_image(request):
    """Image upload page view."""
    return render(request, 'sharpening_app/upload.html')


def webcam_demo(request):
    """Webcam demo page view."""
    return render(request, 'sharpening_app/webcam.html')


def gallery(request):
    """Gallery page view."""
    enhancements = ImageEnhancement.objects.filter(status='completed').order_by('-upload_time')
    
    context = {
        'enhancements': enhancements,
    }
    return render(request, 'sharpening_app/gallery.html', context)


def about(request):
    """About page view."""
    return render(request, 'sharpening_app/about.html')


@csrf_exempt
@require_http_methods(["POST"])
def enhance_image_api(request):
    """API endpoint to enhance uploaded images."""
    try:
        if 'image' not in request.FILES:
            return JsonResponse({'error': 'No image provided'}, status=400)
        
        uploaded_file = request.FILES['image']
        
        # Create enhancement record
        enhancement = ImageEnhancement.objects.create(
            original_image=uploaded_file,
            status='processing'
        )
        
        try:
            # Load image
            image = Image.open(uploaded_file)
            image_array = np.array(image)
            
            # Store metadata
            enhancement.image_width = image.width
            enhancement.image_height = image.height
            enhancement.file_size = uploaded_file.size
            enhancement.save()
            
            # Convert to BGR if needed
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                image_array = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            # Enhance image
            enhanced_array, processing_time = sharpener.enhance_image(image_array)
            
            # Create comparison image
            comparison_array = create_comparison_image(image_array, enhanced_array)
            
            # Save enhanced and comparison images
            enhanced_image = array_to_django_file(enhanced_array, f"enhanced_{enhancement.id}.jpg")
            comparison_image = array_to_django_file(comparison_array, f"comparison_{enhancement.id}.jpg")
            
            enhancement.enhanced_image = enhanced_image
            enhancement.comparison_image = comparison_image
            enhancement.processing_time = processing_time
            enhancement.status = 'completed'
            enhancement.save()
            
            return JsonResponse({
                'success': True,
                'enhancement_id': str(enhancement.id),
                'processing_time': processing_time,
                'original_url': enhancement.original_image.url,
                'enhanced_url': enhancement.enhanced_image.url,
                'comparison_url': enhancement.comparison_image.url,
            })
            
        except Exception as e:
            enhancement.status = 'failed'
            enhancement.error_message = str(e)
            enhancement.save()
            return JsonResponse({'error': f'Enhancement failed: {e}'}, status=500)
            
    except Exception as e:
        return JsonResponse({'error': f'Upload failed: {e}'}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def enhance_webcam_frame(request):
    """API endpoint to enhance webcam frames."""
    try:
        data = json.loads(request.body)
        image_data = data.get('image')
        
        if not image_data:
            return JsonResponse({'error': 'No image data provided'}, status=400)
        
        # Decode base64 image
        image_data = image_data.split(',')[1]  # Remove data:image/jpeg;base64,
        image_bytes = base64.b64decode(image_data)
        
        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        image_array = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # Enhance image
        enhanced_array, processing_time = sharpener.enhance_image(image_array)
        
        # Convert back to base64
        _, buffer = cv2.imencode('.jpg', enhanced_array)
        enhanced_base64 = base64.b64encode(buffer).decode('utf-8')
        
        return JsonResponse({
            'success': True,
            'enhanced_image': f'data:image/jpeg;base64,{enhanced_base64}',
            'processing_time': processing_time,
            'fps': 1.0 / processing_time if processing_time > 0 else 0
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Frame enhancement failed: {e}'}, status=500)


def get_enhancement(request, enhancement_id):
    """Get enhancement details."""
    try:
        enhancement = get_object_or_404(ImageEnhancement, id=enhancement_id)
        
        return JsonResponse({
            'id': str(enhancement.id),
            'status': enhancement.status,
            'upload_time': enhancement.upload_time.isoformat(),
            'processing_time': enhancement.processing_time,
            'image_width': enhancement.image_width,
            'image_height': enhancement.image_height,
            'file_size': enhancement.file_size,
            'original_url': enhancement.original_image.url if enhancement.original_image else None,
            'enhanced_url': enhancement.enhanced_image.url if enhancement.enhanced_image else None,
            'comparison_url': enhancement.comparison_image.url if enhancement.comparison_image else None,
            'error_message': enhancement.error_message,
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@require_http_methods(["DELETE"])
def delete_enhancement(request, enhancement_id):
    """Delete an enhancement."""
    try:
        enhancement = get_object_or_404(ImageEnhancement, id=enhancement_id)
        enhancement.delete()
        
        return JsonResponse({'success': True})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def download_original(request, enhancement_id):
    """Download original image."""
    enhancement = get_object_or_404(ImageEnhancement, id=enhancement_id)
    
    if not enhancement.original_image:
        raise Http404("Original image not found")
    
    response = HttpResponse(enhancement.original_image.read(), content_type='image/jpeg')
    response['Content-Disposition'] = f'attachment; filename="original_{enhancement_id}.jpg"'
    return response


def download_enhanced(request, enhancement_id):
    """Download enhanced image."""
    enhancement = get_object_or_404(ImageEnhancement, id=enhancement_id)
    
    if not enhancement.enhanced_image:
        raise Http404("Enhanced image not found")
    
    response = HttpResponse(enhancement.enhanced_image.read(), content_type='image/jpeg')
    response['Content-Disposition'] = f'attachment; filename="enhanced_{enhancement_id}.jpg"'
    return response


def download_comparison(request, enhancement_id):
    """Download comparison image."""
    enhancement = get_object_or_404(ImageEnhancement, id=enhancement_id)
    
    if not enhancement.comparison_image:
        raise Http404("Comparison image not found")
    
    response = HttpResponse(enhancement.comparison_image.read(), content_type='image/jpeg')
    response['Content-Disposition'] = f'attachment; filename="comparison_{enhancement_id}.jpg"'
    return response


def performance_stats(request):
    """Get performance statistics."""
    try:
        stats = ImageEnhancement.objects.filter(status='completed').aggregate(
            total_enhancements=Count('id'),
            avg_processing_time=Avg('processing_time'),
            avg_file_size=Avg('file_size'),
        )
        
        recent_enhancements = ImageEnhancement.objects.filter(
            status='completed'
        ).order_by('-upload_time')[:10]
        
        recent_times = [e.processing_time for e in recent_enhancements if e.processing_time]
        avg_fps = sum(1.0/t for t in recent_times) / len(recent_times) if recent_times else 0
        
        return JsonResponse({
            'total_enhancements': stats['total_enhancements'] or 0,
            'avg_processing_time': stats['avg_processing_time'] or 0,
            'avg_file_size': stats['avg_file_size'] or 0,
            'avg_fps': avg_fps,
            'model_loaded': sharpener._model is not None,
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# Helper functions
def create_comparison_image(original, enhanced):
    """Create side-by-side comparison image."""
    # Ensure both images have the same height
    h1, w1 = original.shape[:2]
    h2, w2 = enhanced.shape[:2]
    
    if h1 != h2:
        target_height = min(h1, h2)
        original = cv2.resize(original, (int(w1 * target_height / h1), target_height))
        enhanced = cv2.resize(enhanced, (int(w2 * target_height / h2), target_height))
    
    # Create comparison
    comparison = np.hstack([original, enhanced])
    
    # Add labels
    cv2.putText(comparison, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    cv2.putText(comparison, "Enhanced", (original.shape[1] + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    
    return comparison


def array_to_django_file(image_array, filename):
    """Convert numpy array to Django file."""
    # Convert BGR to RGB for PIL
    image_rgb = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
    
    # Convert to PIL Image
    pil_image = Image.fromarray(image_rgb)
    
    # Save to BytesIO
    buffer = BytesIO()
    pil_image.save(buffer, format='JPEG', quality=95)
    buffer.seek(0)
    
    # Create Django file
    django_file = ContentFile(buffer.read(), name=filename)
    
    return django_file
