"""Simple performance test for trained student model."""

import os
import argparse
import torch
import cv2
import numpy as np
import time
import sys
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.models.student_models import LightweightSR
from src.utils.metrics import ImageMetrics


def load_model(model_path: str, device: str = 'cpu'):
    """Load trained model."""
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Create model with same parameters as training
    model = LightweightSR(
        in_channels=3,
        out_channels=3,
        channels=32,  # Same as training
        num_blocks=4,
        upscale_factor=1
    )
    
    # Load state dict
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    model.to(device)
    
    return model, checkpoint


def create_test_image(width: int, height: int) -> np.ndarray:
    """Create a test image with various patterns."""
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Add gradient background
    for i in range(height):
        for j in range(width):
            image[i, j] = [
                int(255 * i / height),
                int(255 * j / width),
                128
            ]
    
    # Add some noise to simulate blur
    noise = np.random.normal(0, 10, image.shape).astype(np.int16)
    image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    return image


def preprocess_image(image: np.ndarray) -> torch.Tensor:
    """Preprocess image for model input."""
    # Convert BGR to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Normalize to [0, 1]
    image_tensor = torch.from_numpy(image_rgb).float() / 255.0
    
    # Add batch dimension and change to CHW format
    image_tensor = image_tensor.permute(2, 0, 1).unsqueeze(0)
    
    return image_tensor


def postprocess_output(output: torch.Tensor) -> np.ndarray:
    """Postprocess model output to image."""
    # Remove batch dimension and convert to HWC
    output = output.squeeze(0).permute(1, 2, 0)
    
    # Clamp to [0, 1] and convert to uint8
    output = torch.clamp(output, 0, 1)
    output_np = (output.cpu().numpy() * 255).astype(np.uint8)
    
    # Convert RGB to BGR
    output_bgr = cv2.cvtColor(output_np, cv2.COLOR_RGB2BGR)
    
    return output_bgr


def benchmark_inference(model, device: str, resolutions: list, num_runs: int = 10):
    """Benchmark inference speed."""
    results = {}

    # Initialize metrics calculator
    metrics_calc = ImageMetrics(device=device)

    print(f"\nBenchmarking inference on {device}...")
    print("-" * 50)
    
    for width, height in resolutions:
        print(f"Testing resolution: {width}x{height}")
        
        # Create test image
        test_image = create_test_image(width, height)
        input_tensor = preprocess_image(test_image).to(device)
        
        # Warm up
        with torch.no_grad():
            for _ in range(3):
                _ = model(input_tensor)
        
        # Benchmark
        times = []
        with torch.no_grad():
            for i in range(num_runs):
                start_time = time.time()
                output = model(input_tensor)
                end_time = time.time()
                times.append(end_time - start_time)
                
                if i == 0:  # Save first output for quality check
                    first_output = postprocess_output(output)
        
        # Calculate statistics
        avg_time = np.mean(times)
        fps = 1.0 / avg_time
        
        # Calculate quality metrics (using input as reference for demonstration)
        try:
            ssim_score = metrics_calc.calculate_ssim(test_image, first_output, data_range=255.0)
        except Exception as e:
            print(f"    Warning: SSIM calculation failed: {e}")
            ssim_score = 0.0

        try:
            psnr_score = metrics_calc.calculate_psnr(test_image, first_output, data_range=255.0)
        except Exception as e:
            print(f"    Warning: PSNR calculation failed: {e}")
            psnr_score = 0.0
        
        results[f"{width}x{height}"] = {
            'avg_inference_time': avg_time,
            'fps': fps,
            'ssim': ssim_score,
            'psnr': psnr_score
        }
        
        print(f"  Average inference time: {avg_time*1000:.2f} ms")
        print(f"  FPS: {fps:.1f}")
        print(f"  SSIM: {ssim_score:.4f}")
        print(f"  PSNR: {psnr_score:.2f} dB")
        print()
    
    return results


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Simple performance test for student model')
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model')
    parser.add_argument('--device', type=str, default='cpu',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--num-runs', type=int, default=10,
                       help='Number of inference runs per resolution')
    parser.add_argument('--output-dir', type=str, default='results/performance',
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    print("Simple Performance Test")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Device: {device}")
    print(f"Number of runs: {args.num_runs}")
    
    # Check if model exists
    if not os.path.exists(args.model_path):
        print(f"Error: Model file not found: {args.model_path}")
        return
    
    # Load model
    try:
        print("\nLoading model...")
        model, checkpoint = load_model(args.model_path, device)
        
        # Print model info
        total_params = sum(p.numel() for p in model.parameters())
        model_size_mb = total_params * 4 / (1024 * 1024)  # Assuming float32
        
        print(f"Model parameters: {total_params:,}")
        print(f"Model size: {model_size_mb:.2f} MB")
        print(f"Training info:")
        print(f"  Epoch: {checkpoint.get('epoch', 'Unknown')}")
        print(f"  Train loss: {checkpoint.get('train_loss', 'Unknown'):.6f}")
        print(f"  Val loss: {checkpoint.get('val_loss', 'Unknown'):.6f}")
        
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Define test resolutions
    resolutions = [
        (256, 256),
        (512, 512),
        (1024, 1024),
        (1920, 1080)
    ]
    
    # Run benchmark
    try:
        results = benchmark_inference(model, device, resolutions, args.num_runs)
        
        # Save results
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        results_file = output_dir / 'performance_results.json'
        with open(results_file, 'w') as f:
            import json
            json.dump(results, f, indent=2)
        
        print(f"Results saved to: {results_file}")
        
        # Print summary
        print("\nPerformance Summary:")
        print("-" * 50)
        for resolution, metrics in results.items():
            fps = metrics['fps']
            ssim = metrics['ssim']
            meets_fps = "✓" if fps >= 30 else "✗"
            meets_ssim = "✓" if ssim >= 0.90 else "✗"
            
            print(f"{resolution}: {fps:.1f} FPS {meets_fps}, SSIM: {ssim:.4f} {meets_ssim}")
        
        # Check if requirements are met
        print("\nRequirement Check:")
        print("-" * 20)
        print(f"Model size < 10MB: {'✓' if model_size_mb < 10 else '✗'} ({model_size_mb:.2f} MB)")
        
        # Check if any resolution meets FPS requirement
        fps_met = any(metrics['fps'] >= 30 for metrics in results.values())
        print(f"FPS ≥ 30: {'✓' if fps_met else '✗'}")
        
        # Check if any resolution meets SSIM requirement
        ssim_met = any(metrics['ssim'] >= 0.90 for metrics in results.values())
        print(f"SSIM ≥ 90%: {'✓' if ssim_met else '✗'}")
        
    except Exception as e:
        print(f"Error during benchmarking: {e}")
        return


if __name__ == '__main__':
    main()
