"""Simple student model training script without knowledge distillation."""

import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import sys
from pathlib import Path
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.data.dataset import create_data_loaders
from src.models.student_models import LightweightSR
from src.utils.config import load_config


def train_epoch(model, dataloader, criterion, optimizer, device):
    """Train for one epoch."""
    model.train()
    total_loss = 0.0
    num_batches = 0
    
    for batch_idx, (inputs, targets) in enumerate(dataloader):
        inputs, targets = inputs.to(device), targets.to(device)
        
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        num_batches += 1
        
        if batch_idx % 10 == 0:
            print(f'  Batch {batch_idx}/{len(dataloader)}, Loss: {loss.item():.6f}')
    
    return total_loss / num_batches


def validate_epoch(model, dataloader, criterion, device):
    """Validate for one epoch."""
    model.eval()
    total_loss = 0.0
    num_batches = 0
    
    with torch.no_grad():
        for inputs, targets in dataloader:
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            total_loss += loss.item()
            num_batches += 1
    
    return total_loss / num_batches


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description='Train simple student model')
    parser.add_argument('--config', type=str, default='config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--epochs', type=int, default=5,
                       help='Number of epochs')
    parser.add_argument('--batch-size', type=int, default=4,
                       help='Batch size')
    parser.add_argument('--device', type=str, default='cpu',
                       choices=['cpu', 'cuda'], help='Device to use')
    parser.add_argument('--data-root', type=str, default='data',
                       help='Root directory for datasets')
    
    args = parser.parse_args()
    
    # Set device
    device = args.device
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, using CPU")
        device = 'cpu'
    
    print("Simple Student Model Training")
    print("=" * 50)
    print(f"Device: {device}")
    print(f"Epochs: {args.epochs}")
    print(f"Batch size: {args.batch_size}")
    print(f"Data root: {args.data_root}")
    
    # Check if datasets exist
    data_root = Path(args.data_root)
    train_dir = data_root / 'train'
    val_dir = data_root / 'val'
    
    if not train_dir.exists() or not val_dir.exists():
        print(f"Error: Training or validation data not found!")
        print(f"Expected directories:")
        print(f"  Training: {train_dir}")
        print(f"  Validation: {val_dir}")
        return
    
    # Load config
    try:
        config = load_config(args.config)
    except Exception as e:
        print(f"Warning: Could not load config: {e}")
        print("Using default configuration")
        config = None
    
    # Create data loaders
    print("\nCreating data loaders...")
    try:
        if config:
            train_loader, val_loader, _ = create_data_loaders(
                config=config,
                train_dir=str(train_dir),
                val_dir=str(val_dir)
            )
        else:
            # Create simple data loaders without config
            from src.data.dataset import ImageSharpeningDataset
            from torchvision import transforms
            
            transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.ToTensor(),
            ])
            
            train_dataset = ImageSharpeningDataset(str(train_dir), transform=transform)
            val_dataset = ImageSharpeningDataset(str(val_dir), transform=transform)
            
            train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False)
        
        print(f"Training samples: {len(train_loader.dataset)}")
        print(f"Validation samples: {len(val_loader.dataset)}")
        print(f"Batch size: {train_loader.batch_size}")
        
    except Exception as e:
        print(f"Error creating data loaders: {e}")
        return
    
    # Create student model
    print("\nCreating student model...")
    try:
        model = LightweightSR(
            in_channels=3,
            out_channels=3,
            channels=32,
            num_blocks=4,
            upscale_factor=1
        ).to(device)
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        print(f"Student model parameters: {total_params:,}")
        
    except Exception as e:
        print(f"Error creating model: {e}")
        return
    
    # Setup training
    criterion = nn.L1Loss()
    optimizer = optim.Adam(model.parameters(), lr=1e-4)
    
    # Create results directory
    results_dir = Path('results/checkpoints')
    results_dir.mkdir(parents=True, exist_ok=True)
    
    # Training loop
    print(f"\nStarting training for {args.epochs} epochs...")
    best_val_loss = float('inf')
    
    for epoch in range(args.epochs):
        start_time = time.time()
        
        print(f"\nEpoch {epoch+1}/{args.epochs}")
        print("-" * 30)
        
        # Train
        train_loss = train_epoch(model, train_loader, criterion, optimizer, device)
        
        # Validate
        val_loss = validate_epoch(model, val_loader, criterion, device)
        
        epoch_time = time.time() - start_time
        
        print(f"Epoch {epoch+1} completed in {epoch_time:.2f}s")
        print(f"Train Loss: {train_loss:.6f}")
        print(f"Val Loss: {val_loss:.6f}")
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'model_params': total_params
            }
            
            model_path = results_dir / 'best_student_model.pth'
            torch.save(checkpoint, model_path)
            print(f"✓ Saved best model to {model_path}")
    
    print(f"\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.6f}")
    print(f"Model saved to: {results_dir / 'best_student_model.pth'}")


if __name__ == '__main__':
    main()
