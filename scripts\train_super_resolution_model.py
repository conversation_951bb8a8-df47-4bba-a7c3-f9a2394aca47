#!/usr/bin/env python3
"""
Train a super-resolution model that upscales images to 1080p HD resolution.
This script creates a model that can take lower resolution images and enhance them to 1080p.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import time
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.student_models import LightweightSR
from utils.metrics import ImageMetrics

class SuperResolutionDataset(Dataset):
    """Dataset for super-resolution training."""
    
    def __init__(self, hr_size=(1080, 1920), lr_scale=2):
        """
        Args:
            hr_size: High resolution target size (height, width)
            lr_scale: Downscaling factor for creating low-res inputs
        """
        self.hr_size = hr_size
        self.lr_size = (hr_size[0] // lr_scale, hr_size[1] // lr_scale)
        self.lr_scale = lr_scale
        
        # Create synthetic training data
        self.num_samples = 1000
        
        # Transforms for HR images
        self.hr_transform = transforms.Compose([
            transforms.Resize(hr_size),
            transforms.ToTensor(),
        ])
        
        # Transforms for LR images (downscale then upscale to create degraded version)
        self.lr_transform = transforms.Compose([
            transforms.Resize(self.lr_size, interpolation=Image.BICUBIC),
            transforms.Resize(hr_size, interpolation=Image.BICUBIC),  # Upscale back
            transforms.ToTensor(),
        ])
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        # Generate synthetic high-resolution image
        hr_image = self._generate_synthetic_image()
        
        # Create low-resolution version
        hr_pil = transforms.ToPILImage()(hr_image)
        lr_image = self.lr_transform(hr_pil)
        
        return lr_image, hr_image
    
    def _generate_synthetic_image(self):
        """Generate synthetic high-resolution image."""
        # Create random patterns and textures
        h, w = self.hr_size
        
        # Base gradient
        x = torch.linspace(0, 1, w).unsqueeze(0).repeat(h, 1)
        y = torch.linspace(0, 1, h).unsqueeze(1).repeat(1, w)
        
        # Create RGB channels with different patterns
        r = torch.sin(x * 10 + y * 5) * 0.5 + 0.5
        g = torch.cos(x * 8 + y * 6) * 0.5 + 0.5
        b = torch.sin(x * 6 + y * 8) * 0.5 + 0.5
        
        # Add noise and details
        noise = torch.randn(3, h, w) * 0.1
        image = torch.stack([r, g, b]) + noise
        
        # Add some sharp features
        if torch.rand(1) > 0.5:
            # Add geometric shapes
            center_x, center_y = torch.randint(w//4, 3*w//4, (1,)), torch.randint(h//4, 3*h//4, (1,))
            radius = torch.randint(50, 200, (1,))
            
            y_grid, x_grid = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
            mask = ((x_grid - center_x) ** 2 + (y_grid - center_y) ** 2) < radius ** 2
            
            for c in range(3):
                image[c][mask] = torch.rand(1) * 0.8 + 0.2
        
        return torch.clamp(image, 0, 1)

def train_super_resolution_model():
    """Train the super-resolution model."""
    print("🚀 Starting Super-Resolution Model Training")
    print("=" * 60)
    
    # Configuration
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Model configuration for 2x super-resolution
    upscale_factor = 2
    hr_size = (540, 960)  # Target HD resolution (scaled down for training efficiency)
    lr_scale = 2  # Input will be 270x480, output 540x960
    
    print(f"Target resolution: {hr_size[1]}x{hr_size[0]} (HD)")
    print(f"Input resolution: {hr_size[1]//lr_scale}x{hr_size[0]//lr_scale}")
    print(f"Upscale factor: {upscale_factor}x")
    
    # Create model
    model = LightweightSR(
        in_channels=3,
        out_channels=3,
        channels=64,  # More channels for better quality
        num_blocks=6,  # More blocks for super-resolution
        upscale_factor=upscale_factor,
        use_attention=True,  # Use attention for better quality
        activation='relu'
    )
    
    model = model.to(device)
    
    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Create dataset and dataloader
    print("\nCreating dataset...")
    dataset = SuperResolutionDataset(hr_size=hr_size, lr_scale=lr_scale)
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True, num_workers=0)
    
    # Loss function and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=50, gamma=0.5)
    
    # Metrics calculator
    metrics_calc = ImageMetrics(device=device.type)
    
    # Training loop
    num_epochs = 100
    best_loss = float('inf')
    
    print(f"\nStarting training for {num_epochs} epochs...")
    print("-" * 60)
    
    for epoch in range(num_epochs):
        model.train()
        epoch_loss = 0.0
        epoch_psnr = 0.0
        num_batches = 0
        
        start_time = time.time()
        
        for batch_idx, (lr_images, hr_images) in enumerate(dataloader):
            lr_images = lr_images.to(device)
            hr_images = hr_images.to(device)
            
            # Forward pass
            optimizer.zero_grad()
            sr_images = model(lr_images)
            
            # Calculate loss
            loss = criterion(sr_images, hr_images)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Calculate metrics
            with torch.no_grad():
                psnr = metrics_calc.calculate_psnr(sr_images, hr_images)
                
            epoch_loss += loss.item()
            epoch_psnr += psnr
            num_batches += 1
            
            if batch_idx % 50 == 0:
                print(f"Epoch {epoch+1:3d}/{num_epochs}, Batch {batch_idx:3d}, "
                      f"Loss: {loss.item():.6f}, PSNR: {psnr:.2f} dB")
        
        # Calculate epoch averages
        avg_loss = epoch_loss / num_batches
        avg_psnr = epoch_psnr / num_batches
        epoch_time = time.time() - start_time
        
        # Update learning rate
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        print(f"Epoch {epoch+1:3d}/{num_epochs} - "
              f"Loss: {avg_loss:.6f}, PSNR: {avg_psnr:.2f} dB, "
              f"LR: {current_lr:.2e}, Time: {epoch_time:.1f}s")
        
        # Save best model
        if avg_loss < best_loss:
            best_loss = avg_loss
            
            # Create checkpoint directory
            checkpoint_dir = Path("results/checkpoints")
            checkpoint_dir.mkdir(parents=True, exist_ok=True)
            
            # Save model
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': avg_loss,
                'psnr': avg_psnr,
                'model_params': {
                    'in_channels': 3,
                    'out_channels': 3,
                    'channels': 64,
                    'num_blocks': 6,
                    'upscale_factor': upscale_factor,
                    'use_attention': True,
                    'activation': 'relu'
                },
                'target_resolution': hr_size,
                'upscale_factor': upscale_factor
            }
            
            torch.save(checkpoint, checkpoint_dir / 'best_super_resolution_model.pth')
            print(f"✓ Saved best model (Loss: {avg_loss:.6f})")
        
        print("-" * 60)
    
    print(f"\n🎉 Training completed!")
    print(f"Best loss: {best_loss:.6f}")
    print(f"Model saved to: results/checkpoints/best_super_resolution_model.pth")

if __name__ == "__main__":
    train_super_resolution_model()
